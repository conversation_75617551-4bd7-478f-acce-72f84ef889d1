/*
 * @Description: 订单金额异常检查服务
 */
import { request } from '@umijs/max';

/** 检查订单金额异常  GET /admin/order-amount-anomalies/check */
export async function checkOrderAmountAnomaly(params?: {
  threshold?: number;
  absoluteThreshold?: number;
  limit?: number;
  orderId?: number;
  skipExisting?: string;
}) {
  return request<API.ResType<API.OrderAmountAnomalyCheckResult>>(
    '/admin/order-amount-anomalies/check',
    {
      method: 'GET',
      params,
    },
  );
}

/** 批量检查并创建异常记录  POST /admin/order-amount-anomalies/batch-check */
export async function batchCheckAnomaly(data?: {
  threshold?: number;
  absoluteThreshold?: number;
  limit?: number;
  autoCreateRecords?: string;
}) {
  return request<API.ResType<API.BatchCheckResult>>(
    '/admin/order-amount-anomalies/batch-check',
    {
      method: 'POST',
      data,
    },
  );
}

/** 获取异常记录列表  GET /admin/order-amount-anomalies/records */
export async function getAnomalyRecords(params?: {
  status?: string;
  anomalyType?: string;
  severity?: string;
  canAutoFix?: boolean;
  limit?: number;
  offset?: number;
}) {
  return request<API.ResType<API.AnomalyRecordsResult>>(
    '/admin/order-amount-anomalies/records',
    {
      method: 'GET',
      params,
    },
  );
}

/** 自动修复异常  POST /admin/order-amount-anomalies/auto-fix/{recordId} */
export async function autoFixAnomaly(recordId: number) {
  return request<API.ResType<API.FixResult>>(
    `/admin/order-amount-anomalies/auto-fix/${recordId}`,
    {
      method: 'POST',
    },
  );
}

/** 批量自动修复异常  POST /admin/order-amount-anomalies/batch-auto-fix */
export async function batchAutoFixAnomaly(data?: {
  recordIds?: string;
  maxAttempts?: number;
  onlyAutoFixable?: string;
}) {
  return request<API.ResType<API.BatchFixResult>>(
    '/admin/order-amount-anomalies/batch-auto-fix',
    {
      method: 'POST',
      data,
    },
  );
}

/** 手动修复异常  POST /admin/order-amount-anomalies/manual-fix/{recordId} */
export async function manualFixAnomaly(
  recordId: number,
  data: {
    originalPrice: number;
    totalFee: number;
    cardDeduction: number;
    couponDeduction: number;
    operatorId: number;
    operatorName: string;
    remark: string;
  },
) {
  return request<API.ResType<API.FixResult>>(
    `/admin/order-amount-anomalies/manual-fix/${recordId}`,
    {
      method: 'POST',
      data,
    },
  );
}

/** 回退修复操作  POST /admin/order-amount-anomalies/revert/{logId} */
export async function revertFix(
  logId: number,
  data: {
    operatorId: number;
    operatorName: string;
    reason: string;
  },
) {
  return request<API.ResType<API.RevertResult>>(
    `/admin/order-amount-anomalies/revert/${logId}`,
    {
      method: 'POST',
      data,
    },
  );
}

/** 获取异常统计报告  GET /admin/order-amount-anomalies/statistics */
export async function getAnomalyStatistics() {
  return request<API.ResType<API.AnomalyStatistics>>(
    '/admin/order-amount-anomalies/statistics',
    {
      method: 'GET',
    },
  );
}

/** 获取修复日志  GET /admin/order-amount-anomalies/fix-logs */
export async function getFixLogs(params?: {
  anomalyRecordId?: number;
  orderId?: number;
  operationType?: string;
  result?: string;
  limit?: number;
  offset?: number;
}) {
  return request<API.ResType<API.FixLogsResult>>(
    '/admin/order-amount-anomalies/fix-logs',
    {
      method: 'GET',
      params,
    },
  );
}

/** 生成异常问题清单  GET /admin/order-amount-anomalies/report */
export async function generateAnomalyReport(params?: {
  status?: string;
  severity?: string;
  includeDetails?: string;
}) {
  return request<API.ResType<API.AnomalyReport>>(
    '/admin/order-amount-anomalies/report',
    {
      method: 'GET',
      params,
    },
  );
}

/** 忽略异常记录  POST /admin/order-amount-anomalies/ignore/{recordId} */
export async function ignoreAnomaly(
  recordId: number,
  data: {
    operatorId: number;
    operatorName: string;
    reason: string;
  },
) {
  return request<API.ResType<API.IgnoreResult>>(
    `/admin/order-amount-anomalies/ignore/${recordId}`,
    {
      method: 'POST',
      data,
    },
  );
}
