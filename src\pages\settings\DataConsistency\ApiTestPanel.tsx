import {
  But<PERSON>,
  Card,
  Space,
  Typography,
  message,
  Row,
  Col,
  Divider,
} from 'antd';
import {
  ApiOutlined,
  BugOutlined,
} from '@ant-design/icons';
import React, { useState } from 'react';
import {
  checkOrderAmountAnomaly,
  batchCheckAnomaly,
  getAnomalyRecords,
  getAnomalyStatistics,
  autoFixAnomaly,
  batchAutoFixAnomaly,
  manualFixAnomaly,
  ignoreAnomaly,
  getFixLogs,
  generateAnomalyReport,
  revertFix,
} from '@/services/data-consistency';

const { Title, Text } = Typography;

/**
 * API测试面板组件
 */
const ApiTestPanel: React.FC = () => {
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const setApiLoading = (apiName: string, isLoading: boolean) => {
    setLoading(prev => ({ ...prev, [apiName]: isLoading }));
  };

  // 测试检查订单金额异常
  const testCheckAnomaly = async () => {
    setApiLoading('checkAnomaly', true);
    try {
      const res = await checkOrderAmountAnomaly({
        threshold: 1,
        limit: 10,
        skipExisting: 'true',
      });
      console.log('检查订单金额异常结果:', res);
      if (!res.errCode) {
        message.success('接口调用成功');
      } else {
        message.error(`接口返回错误: ${res.msg}`);
      }
    } catch (error: any) {
      console.error('检查订单金额异常失败:', error);
      message.error(`接口调用失败: ${error.message}`);
    } finally {
      setApiLoading('checkAnomaly', false);
    }
  };

  // 测试批量检查
  const testBatchCheck = async () => {
    setApiLoading('batchCheck', true);
    try {
      const res = await batchCheckAnomaly({
        threshold: 1,
        limit: 10,
        autoCreateRecords: 'true',
      });
      console.log('批量检查结果:', res);
      if (!res.errCode) {
        message.success('接口调用成功');
      } else {
        message.error(`接口返回错误: ${res.msg}`);
      }
    } catch (error: any) {
      console.error('批量检查失败:', error);
      message.error(`接口调用失败: ${error.message}`);
    } finally {
      setApiLoading('batchCheck', false);
    }
  };

  // 测试获取异常记录
  const testGetRecords = async () => {
    setApiLoading('getRecords', true);
    try {
      const res = await getAnomalyRecords({
        limit: 10,
        offset: 0,
      });
      console.log('获取异常记录结果:', res);
      if (!res.errCode) {
        message.success('接口调用成功');
      } else {
        message.error(`接口返回错误: ${res.msg}`);
      }
    } catch (error: any) {
      console.error('获取异常记录失败:', error);
      message.error(`接口调用失败: ${error.message}`);
    } finally {
      setApiLoading('getRecords', false);
    }
  };

  // 测试获取统计信息
  const testGetStatistics = async () => {
    setApiLoading('getStatistics', true);
    try {
      const res = await getAnomalyStatistics();
      console.log('获取统计信息结果:', res);
      if (!res.errCode) {
        message.success('接口调用成功');
      } else {
        message.error(`接口返回错误: ${res.msg}`);
      }
    } catch (error: any) {
      console.error('获取统计信息失败:', error);
      message.error(`接口调用失败: ${error.message}`);
    } finally {
      setApiLoading('getStatistics', false);
    }
  };

  // 测试获取修复日志
  const testGetFixLogs = async () => {
    setApiLoading('getFixLogs', true);
    try {
      const res = await getFixLogs({
        limit: 10,
        offset: 0,
      });
      console.log('获取修复日志结果:', res);
      if (!res.errCode) {
        message.success('接口调用成功');
      } else {
        message.error(`接口返回错误: ${res.msg}`);
      }
    } catch (error: any) {
      console.error('获取修复日志失败:', error);
      message.error(`接口调用失败: ${error.message}`);
    } finally {
      setApiLoading('getFixLogs', false);
    }
  };

  // 测试生成异常报告
  const testGenerateReport = async () => {
    setApiLoading('generateReport', true);
    try {
      const res = await generateAnomalyReport({
        includeDetails: 'true',
      });
      console.log('生成异常报告结果:', res);
      if (!res.errCode) {
        message.success('接口调用成功');
      } else {
        message.error(`接口返回错误: ${res.msg}`);
      }
    } catch (error: any) {
      console.error('生成异常报告失败:', error);
      message.error(`接口调用失败: ${error.message}`);
    } finally {
      setApiLoading('generateReport', false);
    }
  };

  return (
    <div>
      <Card title={
        <span>
          <BugOutlined style={{ marginRight: 8 }} />
          API接口测试
        </span>
      }>
        <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
          点击下面的按钮测试各个API接口，查看浏览器控制台和网络请求了解详细信息。
        </Text>

        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card size="small" title="检测相关接口">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  type="primary"
                  icon={<ApiOutlined />}
                  loading={loading.checkAnomaly}
                  onClick={testCheckAnomaly}
                  block
                >
                  检查订单金额异常
                </Button>
                <Button
                  icon={<ApiOutlined />}
                  loading={loading.batchCheck}
                  onClick={testBatchCheck}
                  block
                >
                  批量检查并创建记录
                </Button>
              </Space>
            </Card>
          </Col>

          <Col span={8}>
            <Card size="small" title="查询相关接口">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  icon={<ApiOutlined />}
                  loading={loading.getRecords}
                  onClick={testGetRecords}
                  block
                >
                  获取异常记录列表
                </Button>
                <Button
                  icon={<ApiOutlined />}
                  loading={loading.getStatistics}
                  onClick={testGetStatistics}
                  block
                >
                  获取异常统计报告
                </Button>
                <Button
                  icon={<ApiOutlined />}
                  loading={loading.getFixLogs}
                  onClick={testGetFixLogs}
                  block
                >
                  获取修复日志
                </Button>
              </Space>
            </Card>
          </Col>

          <Col span={8}>
            <Card size="small" title="其他接口">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  icon={<ApiOutlined />}
                  loading={loading.generateReport}
                  onClick={testGenerateReport}
                  block
                >
                  生成异常问题清单
                </Button>
                <Button
                  icon={<ApiOutlined />}
                  disabled
                  block
                >
                  自动修复异常 (需recordId)
                </Button>
                <Button
                  icon={<ApiOutlined />}
                  disabled
                  block
                >
                  手动修复异常 (需recordId)
                </Button>
              </Space>
            </Card>
          </Col>
        </Row>

        <Divider />

        <Card size="small" title="接口地址列表" style={{ marginTop: 16 }}>
          <Row gutter={[16, 8]}>
            <Col span={12}>
              <Text code>GET /admin/order-amount-anomaly/check</Text>
            </Col>
            <Col span={12}>
              <Text code>POST /admin/order-amount-anomaly/batch-check</Text>
            </Col>
            <Col span={12}>
              <Text code>GET /admin/order-amount-anomaly/records</Text>
            </Col>
            <Col span={12}>
              <Text code>POST /admin/order-amount-anomaly/auto-fix/{'{recordId}'}</Text>
            </Col>
            <Col span={12}>
              <Text code>POST /admin/order-amount-anomaly/batch-auto-fix</Text>
            </Col>
            <Col span={12}>
              <Text code>POST /admin/order-amount-anomaly/manual-fix/{'{recordId}'}</Text>
            </Col>
            <Col span={12}>
              <Text code>POST /admin/order-amount-anomaly/revert/{'{logId}'}</Text>
            </Col>
            <Col span={12}>
              <Text code>GET /admin/order-amount-anomaly/statistics</Text>
            </Col>
            <Col span={12}>
              <Text code>GET /admin/order-amount-anomaly/fix-logs</Text>
            </Col>
            <Col span={12}>
              <Text code>GET /admin/order-amount-anomaly/report</Text>
            </Col>
            <Col span={12}>
              <Text code>POST /admin/order-amount-anomaly/ignore/{'{recordId}'}</Text>
            </Col>
          </Row>
        </Card>
      </Card>
    </div>
  );
};

export default ApiTestPanel;
