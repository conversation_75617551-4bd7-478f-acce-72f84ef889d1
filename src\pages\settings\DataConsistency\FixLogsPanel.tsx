import {
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Modal,
  Card,
  Row,
  Col,
  Descriptions,
  Typography,
  Input,
  Select,
  DatePicker,
  Form,
} from 'antd';
import {
  ReloadOutlined,
  EyeOutlined,
  RollbackOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import React, { useState } from 'react';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Text } = Typography;

interface FixLogsPanelProps {
  loading: boolean;
  data?: API.FixLogsResult;
  onRefresh: (params?: any) => void;
}

/**
 * 修复日志面板组件
 */
const FixLogsPanel: React.FC<FixLogsPanelProps> = ({
  loading,
  data,
  onRefresh,
}) => {
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentLog, setCurrentLog] = useState<any>(null);
  const [searchForm] = Form.useForm();

  // 操作类型标签颜色
  const getOperationTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'auto_fix': 'blue',
      'manual_fix': 'green',
      'revert': 'orange',
      'ignore': 'default',
    };
    return colors[type] || 'default';
  };

  // 结果标签颜色
  const getResultColor = (result: string) => {
    const colors: Record<string, string> = {
      'success': 'success',
      'failed': 'error',
      'reverted': 'warning',
    };
    return colors[result] || 'default';
  };

  // 查看详情
  const handleViewDetail = (log: any) => {
    setCurrentLog(log);
    setDetailVisible(true);
  };

  // 搜索日志
  const handleSearch = () => {
    const values = searchForm.getFieldsValue();
    const params: any = {};
    
    if (values.orderId) {
      params.orderId = values.orderId;
    }
    if (values.operationType) {
      params.operationType = values.operationType;
    }
    if (values.result) {
      params.result = values.result;
    }
    if (values.dateRange && values.dateRange.length === 2) {
      params.startDate = values.dateRange[0].format('YYYY-MM-DD');
      params.endDate = values.dateRange[1].format('YYYY-MM-DD');
    }
    
    onRefresh(params);
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    onRefresh();
  };

  const columns = [
    {
      title: '订单编号',
      dataIndex: 'orderSn',
      key: 'orderSn',
      width: 150,
    },
    {
      title: '操作类型',
      dataIndex: 'operationType',
      key: 'operationType',
      width: 120,
      render: (type: string) => {
        const typeMap: Record<string, string> = {
          'auto_fix': '自动修复',
          'manual_fix': '手动修复',
          'revert': '回退操作',
          'ignore': '忽略异常',
        };
        return (
          <Tag color={getOperationTypeColor(type)}>
            {typeMap[type] || type}
          </Tag>
        );
      },
    },
    {
      title: '操作结果',
      dataIndex: 'result',
      key: 'result',
      width: 100,
      render: (result: string) => {
        const resultMap: Record<string, string> = {
          'success': '成功',
          'failed': '失败',
          'reverted': '已回退',
        };
        return (
          <Tag color={getResultColor(result)}>
            {resultMap[result] || result}
          </Tag>
        );
      },
    },
    {
      title: '操作人员',
      dataIndex: 'operatorName',
      key: 'operatorName',
      width: 120,
    },
    {
      title: '操作时间',
      dataIndex: 'operatedAt',
      key: 'operatedAt',
      width: 150,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
      render: (remark: string) => (
        <Tooltip title={remark}>
          <Text ellipsis style={{ maxWidth: 200 }}>
            {remark || '-'}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: any) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            >
              详情
            </Button>
          </Tooltip>
          {record.result === 'success' && record.operationType !== 'revert' && (
            <Tooltip title="回退操作">
              <Button
                type="link"
                size="small"
                icon={<RollbackOutlined />}
                danger
                onClick={() => {
                  Modal.confirm({
                    title: '确认回退操作',
                    content: `确定要回退订单 ${record.orderSn} 的修复操作吗？`,
                    onOk: () => {
                      console.log('回退操作:', record.id);
                      // 这里调用回退API
                    },
                  });
                }}
              >
                回退
              </Button>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 搜索条件 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Form form={searchForm} layout="inline">
          <Form.Item label="订单ID" name="orderId">
            <Input placeholder="请输入订单ID" style={{ width: 150 }} />
          </Form.Item>
          <Form.Item label="操作类型" name="operationType">
            <Select placeholder="请选择操作类型" style={{ width: 150 }} allowClear>
              <Option value="auto_fix">自动修复</Option>
              <Option value="manual_fix">手动修复</Option>
              <Option value="revert">回退操作</Option>
              <Option value="ignore">忽略异常</Option>
            </Select>
          </Form.Item>
          <Form.Item label="操作结果" name="result">
            <Select placeholder="请选择操作结果" style={{ width: 120 }} allowClear>
              <Option value="success">成功</Option>
              <Option value="failed">失败</Option>
              <Option value="reverted">已回退</Option>
            </Select>
          </Form.Item>
          <Form.Item label="操作时间" name="dateRange">
            <RangePicker />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
                loading={loading}
              >
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => onRefresh()}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 日志列表 */}
      <Table
        columns={columns}
        dataSource={data?.logs || []}
        loading={loading}
        rowKey="id"
        pagination={{
          total: data?.total || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
        scroll={{ x: 1000 }}
      />

      {/* 详情弹窗 */}
      <Modal
        title="修复日志详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {currentLog && (
          <div>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="订单编号">
                {currentLog.orderSn}
              </Descriptions.Item>
              <Descriptions.Item label="操作类型">
                <Tag color={getOperationTypeColor(currentLog.operationType)}>
                  {currentLog.operationType === 'auto_fix' ? '自动修复' :
                   currentLog.operationType === 'manual_fix' ? '手动修复' :
                   currentLog.operationType === 'revert' ? '回退操作' :
                   currentLog.operationType === 'ignore' ? '忽略异常' : currentLog.operationType}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="操作结果">
                <Tag color={getResultColor(currentLog.result)}>
                  {currentLog.result === 'success' ? '成功' :
                   currentLog.result === 'failed' ? '失败' :
                   currentLog.result === 'reverted' ? '已回退' : currentLog.result}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="操作人员">
                {currentLog.operatorName}
              </Descriptions.Item>
              <Descriptions.Item label="操作时间" span={2}>
                {new Date(currentLog.operatedAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="备注" span={2}>
                {currentLog.remark || '-'}
              </Descriptions.Item>
            </Descriptions>

            {/* 数据变更对比 */}
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Card title="修复前数据" size="small">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="原价">
                      ¥{currentLog.beforeData?.originalPrice?.toFixed(2) || '0.00'}
                    </Descriptions.Item>
                    <Descriptions.Item label="实付金额">
                      ¥{currentLog.beforeData?.totalFee?.toFixed(2) || '0.00'}
                    </Descriptions.Item>
                    <Descriptions.Item label="权益卡抵扣">
                      ¥{currentLog.beforeData?.cardDeduction?.toFixed(2) || '0.00'}
                    </Descriptions.Item>
                    <Descriptions.Item label="代金券抵扣">
                      ¥{currentLog.beforeData?.couponDeduction?.toFixed(2) || '0.00'}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="修复后数据" size="small">
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="原价">
                      ¥{currentLog.afterData?.originalPrice?.toFixed(2) || '0.00'}
                    </Descriptions.Item>
                    <Descriptions.Item label="实付金额">
                      ¥{currentLog.afterData?.totalFee?.toFixed(2) || '0.00'}
                    </Descriptions.Item>
                    <Descriptions.Item label="权益卡抵扣">
                      ¥{currentLog.afterData?.cardDeduction?.toFixed(2) || '0.00'}
                    </Descriptions.Item>
                    <Descriptions.Item label="代金券抵扣">
                      ¥{currentLog.afterData?.couponDeduction?.toFixed(2) || '0.00'}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FixLogsPanel;
