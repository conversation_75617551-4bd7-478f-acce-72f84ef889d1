import {
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Modal,
  Form,
  InputNumber,
  Input,
  message,
  Card,
  Row,
  Col,
  Statistic,
} from 'antd';
import {
  ReloadOutlined,
  ToolOutlined,
  EditOutlined,
  EyeInvisibleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import React, { useState } from 'react';

interface AnomalyRecordsListProps {
  loading: boolean;
  data?: API.AnomalyRecordsResult;
  onRefresh: (params?: any) => void;
  onAutoFix: (recordId: number) => void;
  onManualFix: (recordId: number, data: any) => void;
  onIgnore: (recordId: number, data: any) => void;
}

/**
 * 异常记录列表组件
 */
const AnomalyRecordsList: React.FC<AnomalyRecordsListProps> = ({
  loading,
  data,
  onRefresh,
  onAutoFix,
  onManualFix,
  onIgnore,
}) => {
  const [manualFixVisible, setManualFixVisible] = useState(false);
  const [ignoreVisible, setIgnoreVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [manualFixForm] = Form.useForm();
  const [ignoreForm] = Form.useForm();

  // 异常类型标签颜色
  const getAnomalyTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'MISSING_ORIGINAL_PRICE': 'red',
      'PRICE_MISMATCH': 'orange',
      'CALCULATION_ERROR': 'volcano',
      'DISCOUNT_ANOMALY': 'gold',
      'OTHER': 'default',
    };
    return colors[type] || 'default';
  };

  // 处理状态标签颜色
  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'pending': 'default',
      'auto_fixing': 'processing',
      'auto_fixed': 'success',
      'auto_fix_failed': 'error',
      'manual_required': 'warning',
      'manual_processing': 'processing',
      'manual_fixed': 'success',
      'ignored': 'default',
    };
    return colors[status] || 'default';
  };

  // 严重程度标签
  const getSeverityTag = (severity: number) => {
    const configs = [
      { level: 1, color: 'green', text: '轻微' },
      { level: 2, color: 'blue', text: '一般' },
      { level: 3, color: 'orange', text: '中等' },
      { level: 4, color: 'red', text: '严重' },
      { level: 5, color: 'purple', text: '极严重' },
    ];
    const config = configs.find(c => c.level === severity) || configs[0];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 自动修复
  const handleAutoFix = (record: any) => {
    Modal.confirm({
      title: '确认自动修复',
      content: `确定要自动修复订单 ${record.orderSn} 的异常吗？`,
      icon: <ExclamationCircleOutlined />,
      onOk: () => onAutoFix(record.id),
    });
  };

  // 手动修复
  const handleManualFix = (record: any) => {
    setCurrentRecord(record);
    manualFixForm.setFieldsValue({
      originalPrice: record.suggestedData?.originalPrice || record.currentData.originalPrice,
      totalFee: record.suggestedData?.totalFee || record.currentData.totalFee,
      cardDeduction: record.suggestedData?.cardDeduction || record.currentData.cardDeduction,
      couponDeduction: record.suggestedData?.couponDeduction || record.currentData.couponDeduction,
      remark: '',
    });
    setManualFixVisible(true);
  };

  // 忽略异常
  const handleIgnore = (record: any) => {
    setCurrentRecord(record);
    ignoreForm.resetFields();
    setIgnoreVisible(true);
  };

  // 提交手动修复
  const submitManualFix = async () => {
    try {
      const values = await manualFixForm.validateFields();
      await onManualFix(currentRecord.id, {
        ...values,
        operatorId: 1, // 这里应该从用户信息中获取
        operatorName: '管理员', // 这里应该从用户信息中获取
      });
      setManualFixVisible(false);
      setCurrentRecord(null);
    } catch (error) {
      console.error('手动修复失败:', error);
    }
  };

  // 提交忽略
  const submitIgnore = async () => {
    try {
      const values = await ignoreForm.validateFields();
      await onIgnore(currentRecord.id, {
        ...values,
        operatorId: 1, // 这里应该从用户信息中获取
        operatorName: '管理员', // 这里应该从用户信息中获取
      });
      setIgnoreVisible(false);
      setCurrentRecord(null);
    } catch (error) {
      console.error('忽略操作失败:', error);
    }
  };

  const columns = [
    {
      title: '订单编号',
      dataIndex: 'orderSn',
      key: 'orderSn',
      width: 150,
    },
    {
      title: '异常类型',
      dataIndex: 'anomalyType',
      key: 'anomalyType',
      width: 120,
      render: (type: string) => (
        <Tag color={getAnomalyTypeColor(type)}>
          {type.replace(/_/g, ' ')}
        </Tag>
      ),
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      width: 80,
      render: (severity: number) => getSeverityTag(severity),
    },
    {
      title: '异常金额',
      dataIndex: 'anomalyAmount',
      key: 'anomalyAmount',
      width: 100,
      render: (amount: number) => `¥${amount?.toFixed(2) || '0.00'}`,
    },
    {
      title: '处理状态',
      dataIndex: 'processStatus',
      key: 'processStatus',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.replace(/_/g, ' ')}
        </Tag>
      ),
    },
    {
      title: '可自动修复',
      dataIndex: 'canAutoFix',
      key: 'canAutoFix',
      width: 100,
      render: (canAutoFix: boolean) => (
        <Tag color={canAutoFix ? 'green' : 'red'}>
          {canAutoFix ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '检测时间',
      dataIndex: 'detectedAt',
      key: 'detectedAt',
      width: 150,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record: any) => (
        <Space size="small">
          {record.canAutoFix && record.processStatus === 'pending' && (
            <Tooltip title="自动修复">
              <Button
                type="primary"
                size="small"
                icon={<ToolOutlined />}
                onClick={() => handleAutoFix(record)}
              >
                自动修复
              </Button>
            </Tooltip>
          )}
          {record.processStatus === 'pending' && (
            <Tooltip title="手动修复">
              <Button
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleManualFix(record)}
              >
                手动修复
              </Button>
            </Tooltip>
          )}
          {record.processStatus === 'pending' && (
            <Tooltip title="忽略异常">
              <Button
                size="small"
                icon={<EyeInvisibleOutlined />}
                onClick={() => handleIgnore(record)}
              >
                忽略
              </Button>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 统计信息 */}
      {data && (
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic title="总异常数" value={data.total} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="待处理"
                value={data.records?.filter(r => r.processStatus === 'pending').length || 0}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已修复"
                value={data.records?.filter(r => r.processStatus.includes('fixed')).length || 0}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="可自动修复"
                value={data.records?.filter(r => r.canAutoFix && r.processStatus === 'pending').length || 0}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 操作栏 */}
      <div style={{ marginBottom: 16 }}>
        <Button
          icon={<ReloadOutlined />}
          onClick={() => onRefresh()}
          loading={loading}
        >
          刷新
        </Button>
      </div>

      {/* 异常记录表格 */}
      <Table
        columns={columns}
        dataSource={data?.records || []}
        loading={loading}
        rowKey="id"
        pagination={{
          total: data?.total || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
        scroll={{ x: 1200 }}
      />

      {/* 手动修复弹窗 */}
      <Modal
        title="手动修复异常"
        open={manualFixVisible}
        onOk={submitManualFix}
        onCancel={() => setManualFixVisible(false)}
        width={600}
      >
        <Form form={manualFixForm} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="原价"
                name="originalPrice"
                rules={[{ required: true, message: '请输入原价' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="实付金额"
                name="totalFee"
                rules={[{ required: true, message: '请输入实付金额' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="权益卡抵扣"
                name="cardDeduction"
                rules={[{ required: true, message: '请输入权益卡抵扣金额' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="代金券抵扣"
                name="couponDeduction"
                rules={[{ required: true, message: '请输入代金券抵扣金额' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  precision={2}
                  min={0}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            label="修复备注"
            name="remark"
            rules={[{ required: true, message: '请输入修复备注' }]}
          >
            <Input.TextArea rows={3} placeholder="请说明修复原因和依据" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 忽略异常弹窗 */}
      <Modal
        title="忽略异常"
        open={ignoreVisible}
        onOk={submitIgnore}
        onCancel={() => setIgnoreVisible(false)}
      >
        <Form form={ignoreForm} layout="vertical">
          <Form.Item
            label="忽略原因"
            name="reason"
            rules={[{ required: true, message: '请输入忽略原因' }]}
          >
            <Input.TextArea rows={3} placeholder="请说明为什么忽略此异常" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AnomalyRecordsList;
