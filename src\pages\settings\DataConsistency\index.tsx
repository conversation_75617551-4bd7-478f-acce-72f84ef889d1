import {
  checkOrderAmountAnomaly,
  batchCheckAnomaly,
  getAnomalyRecords,
  getAnomalyStatistics,
  autoFixAnomaly,
  batchAutoFixAnomaly,
  manualFixAnomaly,
  ignoreAnomaly,
  getFixLogs,
  generateAnomalyReport,
} from '@/services/data-consistency';
import { PageContainer } from '@ant-design/pro-components';
import { Alert, Card, Col, Row, Tabs, Typography, message } from 'antd';
import React, { useEffect, useState } from 'react';
import AnomalyDetectionPanel from './AnomalyDetectionPanel';
import AnomalyRecordsList from './AnomalyRecordsList';
import FixOperationPanel from './FixOperationPanel';
import StatisticsReport from './StatisticsReport';
import FixLogsPanel from './FixLogsPanel';
import './index.less';

const { Paragraph } = Typography;

/**
 * 订单金额异常检查主页面
 */
const DataConsistency: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [recordsLoading, setRecordsLoading] = useState(false);
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  const [logsLoading, setLogsLoading] = useState(false);
  const [statistics, setStatistics] = useState<API.AnomalyStatistics>();
  const [anomalyRecords, setAnomalyRecords] = useState<API.AnomalyRecordsResult>();
  const [fixLogs, setFixLogs] = useState<API.FixLogsResult>();
  const [activeTab, setActiveTab] = useState('detection');

  // 加载统计信息
  const loadStatistics = async () => {
    setStatisticsLoading(true);
    try {
      const res = await getAnomalyStatistics();
      if (!res.errCode) {
        setStatistics(res.data);
      } else {
        message.error(res.msg || '获取统计信息失败');
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
      message.error('获取统计信息失败');
    } finally {
      setStatisticsLoading(false);
    }
  };

  // 加载异常记录
  const loadAnomalyRecords = async (params?: any) => {
    setRecordsLoading(true);
    try {
      const res = await getAnomalyRecords(params);
      if (!res.errCode) {
        setAnomalyRecords(res.data);
      } else {
        message.error(res.msg || '获取异常记录失败');
      }
    } catch (error) {
      console.error('加载异常记录失败:', error);
      message.error('获取异常记录失败');
    } finally {
      setRecordsLoading(false);
    }
  };

  // 加载修复日志
  const loadFixLogs = async (params?: any) => {
    setLogsLoading(true);
    try {
      const res = await getFixLogs(params);
      if (!res.errCode) {
        setFixLogs(res.data);
      } else {
        message.error(res.msg || '获取修复日志失败');
      }
    } catch (error) {
      console.error('加载修复日志失败:', error);
      message.error('获取修复日志失败');
    } finally {
      setLogsLoading(false);
    }
  };

  // 检查订单金额异常
  const handleCheckAnomaly = async (params?: any) => {
    setLoading(true);
    try {
      const res = await checkOrderAmountAnomaly(params);
      if (!res.errCode) {
        message.success(`检查完成，发现 ${res.data?.anomalyCount || 0} 个异常`);
        // 刷新异常记录列表
        await loadAnomalyRecords();
        await loadStatistics();
      } else {
        message.error(res.msg || '异常检查失败');
      }
    } catch (error) {
      message.error('异常检查失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量检查并创建记录
  const handleBatchCheck = async (params?: any) => {
    setLoading(true);
    try {
      const res = await batchCheckAnomaly(params);
      if (!res.errCode) {
        message.success(res.data?.message || '批量检查完成');
        await loadAnomalyRecords();
        await loadStatistics();
      } else {
        message.error(res.msg || '批量检查失败');
      }
    } catch (error) {
      message.error('批量检查失败');
    } finally {
      setLoading(false);
    }
  };

  // 自动修复异常
  const handleAutoFix = async (recordId: number) => {
    setLoading(true);
    try {
      const res = await autoFixAnomaly(recordId);
      if (!res.errCode) {
        message.success(res.data?.message || '自动修复成功');
        await loadAnomalyRecords();
        await loadFixLogs();
        await loadStatistics();
      } else {
        message.error(res.msg || '自动修复失败');
      }
    } catch (error) {
      message.error('自动修复失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量自动修复
  const handleBatchAutoFix = async (params?: any) => {
    setLoading(true);
    try {
      const res = await batchAutoFixAnomaly(params);
      if (!res.errCode) {
        const result = res.data;
        message.success(`批量修复完成，成功 ${result?.successCount || 0} 个，失败 ${result?.failedCount || 0} 个`);
        await loadAnomalyRecords();
        await loadFixLogs();
        await loadStatistics();
      } else {
        message.error(res.msg || '批量修复失败');
      }
    } catch (error) {
      message.error('批量修复失败');
    } finally {
      setLoading(false);
    }
  };

  // 手动修复异常
  const handleManualFix = async (recordId: number, data: any) => {
    setLoading(true);
    try {
      const res = await manualFixAnomaly(recordId, data);
      if (!res.errCode) {
        message.success(res.data?.message || '手动修复成功');
        await loadAnomalyRecords();
        await loadFixLogs();
        await loadStatistics();
      } else {
        message.error(res.msg || '手动修复失败');
      }
    } catch (error) {
      message.error('手动修复失败');
    } finally {
      setLoading(false);
    }
  };

  // 忽略异常
  const handleIgnoreAnomaly = async (recordId: number, data: any) => {
    setLoading(true);
    try {
      const res = await ignoreAnomaly(recordId, data);
      if (!res.errCode) {
        message.success(res.data?.message || '已忽略异常');
        await loadAnomalyRecords();
        await loadStatistics();
      } else {
        message.error(res.msg || '忽略操作失败');
      }
    } catch (error) {
      message.error('忽略操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 生成异常报告
  const handleGenerateReport = async (params?: any) => {
    setLoading(true);
    try {
      const res = await generateAnomalyReport(params);
      if (!res.errCode) {
        message.success('报告生成成功');
        // 这里可以处理报告下载或显示
        console.log('异常报告:', res.data);
      } else {
        message.error(res.msg || '报告生成失败');
      }
    } catch (error) {
      message.error('报告生成失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 暂时注释掉自动加载，等后端接口实现后再启用
    // loadStatistics();
    // loadAnomalyRecords();
  }, []);

  const tabItems = [
    {
      key: 'detection',
      label: '异常检测',
      children: (
        <AnomalyDetectionPanel
          loading={loading}
          onCheckAnomaly={handleCheckAnomaly}
          onBatchCheck={handleBatchCheck}
          onGenerateReport={handleGenerateReport}
        />
      ),
    },
    {
      key: 'records',
      label: '异常记录',
      children: (
        <AnomalyRecordsList
          loading={recordsLoading}
          data={anomalyRecords}
          onRefresh={loadAnomalyRecords}
          onAutoFix={handleAutoFix}
          onManualFix={handleManualFix}
          onIgnore={handleIgnoreAnomaly}
        />
      ),
    },
    {
      key: 'operations',
      label: '修复操作',
      children: (
        <FixOperationPanel
          loading={loading}
          onBatchAutoFix={handleBatchAutoFix}
          onRefresh={() => {
            loadAnomalyRecords();
            loadStatistics();
          }}
        />
      ),
    },
    {
      key: 'statistics',
      label: '统计报告',
      children: (
        <StatisticsReport
          data={statistics}
          loading={statisticsLoading}
          onRefresh={loadStatistics}
        />
      ),
    },
    {
      key: 'logs',
      label: '修复日志',
      children: (
        <FixLogsPanel
          loading={logsLoading}
          data={fixLogs}
          onRefresh={loadFixLogs}
        />
      ),
    },
  ];

  return (
    <PageContainer
      title="数据一致性维护"
      className="order-amount-anomaly"
      content={
        <div>
          <Paragraph>
            订单金额异常检查系统用于检测、修复和管理订单金额异常，包括原价与实付不匹配、优惠金额异常等问题。
          </Paragraph>
          <Alert
            message="功能说明"
            description={
              <div>
                <div>
                  <strong>异常检测：</strong>
                  自动检测订单金额异常，包括原价缺失、价格不匹配、计算错误等问题
                </div>
                <div>
                  <strong>自动修复：</strong>
                  对可自动处理的异常进行修复，提高处理效率
                </div>
                <div>
                  <strong>手动修复：</strong>
                  对复杂异常提供人工干预修复功能，确保数据准确性
                </div>
                <div>
                  <strong>处理记录：</strong>
                  完整记录所有处理过程，支持回退和审计
                </div>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <Alert
            message="操作建议"
            description="建议按照以下步骤进行操作：定期执行异常检测 → 查看异常记录列表 → 执行自动修复或手动修复 → 查看统计报告监控异常情况 → 必要时查看修复日志"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </div>
      }
    >
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={tabItems}
            />
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default DataConsistency;
