import {
  But<PERSON>,
  Card,
  Col,
  Form,
  InputNumber,
  Row,
  Space,
  Typography,
  Tooltip,
  Divider,
} from 'antd';
import {
  SearchOutlined,
  ScanOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import React, { useState } from 'react';

const { Title, Text } = Typography;

interface AnomalyDetectionPanelProps {
  loading: boolean;
  onCheckAnomaly: (params?: any) => void;
  onBatchCheck: (params?: any) => void;
  onGenerateReport: (params?: any) => void;
}

/**
 * 异常检测面板组件
 */
const AnomalyDetectionPanel: React.FC<AnomalyDetectionPanelProps> = ({
  loading,
  onCheckAnomaly,
  onBatchCheck,
  onGenerateReport,
}) => {
  const [form] = Form.useForm();
  const [checkParams, setCheckParams] = useState({
    threshold: 1,
    absoluteThreshold: undefined as number | undefined,
    limit: 100,
    skipExisting: 'true',
  });

  // 执行异常检测
  const handleCheck = () => {
    onCheckAnomaly(checkParams);
  };

  // 执行批量检查
  const handleBatchCheck = () => {
    onBatchCheck({
      ...checkParams,
      autoCreateRecords: 'true',
    });
  };

  // 生成异常报告
  const handleGenerateReport = () => {
    onGenerateReport({
      includeDetails: 'true',
    });
  };

  return (
    <div>
      {/* 检测参数配置 */}
      <Card title="检测参数配置" size="small" style={{ marginBottom: 16 }}>
        <Form
          form={form}
          layout="inline"
          initialValues={checkParams}
          onValuesChange={(_, values) => setCheckParams(values)}
        >
          <Form.Item
            label="百分比阈值"
            name="threshold"
            tooltip="当价格差异超过此百分比时标记为异常，设为0则检测任何不一致"
          >
            <InputNumber
              min={0}
              max={50}
              step={0.1}
              precision={1}
              addonAfter="%"
              style={{ width: 120 }}
            />
          </Form.Item>
          <Form.Item
            label="绝对金额阈值"
            name="absoluteThreshold"
            tooltip="绝对金额阈值（元），优先级高于百分比阈值"
          >
            <InputNumber
              min={0}
              max={1000}
              step={0.1}
              precision={2}
              addonAfter="元"
              style={{ width: 120 }}
              placeholder="可选"
            />
          </Form.Item>
          <Form.Item
            label="检查数量限制"
            name="limit"
            tooltip="单次检查的订单数量限制"
          >
            <InputNumber
              min={10}
              max={1000}
              step={10}
              style={{ width: 120 }}
            />
          </Form.Item>
          <Form.Item
            label="跳过已记录"
            name="skipExisting"
            tooltip="是否跳过已经记录的异常"
          >
            <Button
              type={checkParams.skipExisting === 'true' ? 'primary' : 'default'}
              size="small"
              onClick={() =>
                setCheckParams({
                  ...checkParams,
                  skipExisting: checkParams.skipExisting === 'true' ? 'false' : 'true',
                })
              }
            >
              {checkParams.skipExisting === 'true' ? '是' : '否'}
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 检测操作 */}
      <Card title="异常检测操作" size="small">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Title level={5}>
              <InfoCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              快速检测
            </Title>
            <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
              快速检测订单金额异常，仅返回检测结果，不创建异常记录。适用于了解当前异常情况。
            </Text>
            <Space>
              <Tooltip title="检测订单金额异常，返回异常列表但不创建记录">
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  loading={loading}
                  onClick={handleCheck}
                >
                  快速检测异常
                </Button>
              </Tooltip>
            </Space>
          </Col>

          <Col span={24}>
            <Divider />
            <Title level={5}>
              <ScanOutlined style={{ marginRight: 8, color: '#52c41a' }} />
              批量检测并记录
            </Title>
            <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
              批量检测异常并自动创建异常记录，便于后续修复处理。建议在业务低峰期执行。
            </Text>
            <Space>
              <Tooltip title="批量检测异常并自动创建异常记录">
                <Button
                  type="primary"
                  icon={<ScanOutlined />}
                  loading={loading}
                  onClick={handleBatchCheck}
                  style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                >
                  批量检测并记录
                </Button>
              </Tooltip>
            </Space>
          </Col>

          <Col span={24}>
            <Divider />
            <Title level={5}>
              <FileTextOutlined style={{ marginRight: 8, color: '#fa8c16' }} />
              异常报告
            </Title>
            <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
              生成详细的异常问题清单报告，包含异常详情和处理建议。
            </Text>
            <Space>
              <Tooltip title="生成异常问题清单报告">
                <Button
                  icon={<FileTextOutlined />}
                  loading={loading}
                  onClick={handleGenerateReport}
                >
                  生成异常报告
                </Button>
              </Tooltip>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 检测说明 */}
      <Card title="检测规则说明" size="small" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Title level={5}>检测维度</Title>
            <ul>
              <li><strong>原价完整性：</strong>检测原价是否缺失或为0</li>
              <li><strong>价格一致性：</strong>原价与订单详情价格之和是否匹配</li>
              <li><strong>计算逻辑：</strong>实付金额 = 原价 - 权益卡抵扣 - 代金券抵扣</li>
              <li><strong>优惠合理性：</strong>优惠总额不能超过订单原价</li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={5}>异常分类</Title>
            <ul>
              <li><strong>MISSING_ORIGINAL_PRICE：</strong>原价缺失</li>
              <li><strong>PRICE_MISMATCH：</strong>原价与计算价格不匹配</li>
              <li><strong>CALCULATION_ERROR：</strong>实付金额计算错误</li>
              <li><strong>DISCOUNT_ANOMALY：</strong>优惠金额异常</li>
              <li><strong>OTHER：</strong>其他异常</li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={5}>阈值设置</Title>
            <ul>
              <li><strong>百分比阈值：</strong>按价格差异百分比判断，设为0检测任何不一致</li>
              <li><strong>绝对金额阈值：</strong>按绝对金额差异判断，优先级更高</li>
              <li><strong>阈值优先级：</strong>绝对金额阈值 {'>'} 百分比阈值</li>
              <li><strong>建议设置：</strong>百分比1%或绝对金额1元</li>
            </ul>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default AnomalyDetectionPanel;
