import {
  Card,
  Col,
  Row,
  Statistic,
  Progress,
  Table,
  Button,
  Typography,
  Empty,
  Spin,
} from 'antd';
import {
  ReloadOutlined,
  PieChartOutlined,
  BarChartOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import React from 'react';
import { formatNumber } from '@/utils/format';

const { Title } = Typography;

interface StatisticsReportProps {
  data?: API.AnomalyStatistics;
  loading?: boolean;
  onRefresh: () => void;
}

/**
 * 统计报告组件
 */
const StatisticsReport: React.FC<StatisticsReportProps> = ({
  data,
  loading = false,
  onRefresh,
}) => {
  if (!data) {
    return (
      <div style={{ textAlign: 'center', padding: 50 }}>
        <Empty description="暂无统计数据">
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={onRefresh}
            loading={loading}
          >
            加载数据
          </Button>
        </Empty>
      </div>
    );
  }

  // 处理状态统计表格列
  const statusColumns = [
    {
      title: '处理状态',
      dataIndex: 'processStatus',
      key: 'processStatus',
      render: (status: string) => {
        const statusMap: Record<string, string> = {
          'pending': '待处理',
          'auto_fixing': '自动修复中',
          'auto_fixed': '自动修复成功',
          'auto_fix_failed': '自动修复失败',
          'manual_required': '需要人工处理',
          'manual_processing': '人工处理中',
          'manual_fixed': '人工处理完成',
          'ignored': '已忽略',
        };
        return statusMap[status] || status;
      },
    },
    {
      title: '数量',
      dataIndex: 'count',
      key: 'count',
      render: (count: number) => (
        <Statistic value={count} valueStyle={{ fontSize: 16 }} />
      ),
    },
    {
      title: '占比',
      key: 'percentage',
      render: (_, record: any) => {
        const percentage = data.totalAnomalies > 0
          ? formatNumber((record.count / data.totalAnomalies) * 100, 1)
          : '0.0';
        return `${percentage}%`;
      },
    },
  ];

  // 异常类型统计表格列
  const typeColumns = [
    {
      title: '异常类型',
      dataIndex: 'anomalyType',
      key: 'anomalyType',
      render: (type: string) => {
        const typeMap: Record<string, string> = {
          'MISSING_ORIGINAL_PRICE': '原价缺失',
          'PRICE_MISMATCH': '原价与计算价格不匹配',
          'CALCULATION_ERROR': '实付金额计算错误',
          'DISCOUNT_ANOMALY': '优惠金额异常',
          'OTHER': '其他异常',
        };
        return typeMap[type] || type;
      },
    },
    {
      title: '数量',
      dataIndex: 'count',
      key: 'count',
      render: (count: number) => (
        <Statistic value={count} valueStyle={{ fontSize: 16 }} />
      ),
    },
    {
      title: '占比',
      key: 'percentage',
      render: (_, record: any) => {
        const percentage = data.totalAnomalies > 0
          ? formatNumber((record.count / data.totalAnomalies) * 100, 1)
          : '0.0';
        return `${percentage}%`;
      },
    },
  ];

  return (
    <Spin spinning={loading}>
      <div>
        {/* 总体统计 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总异常数"
              value={data.totalAnomalies}
              prefix={<PieChartOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="可自动修复"
              value={data.autoFixStatistics.autoFixableCount}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已自动修复"
              value={data.autoFixStatistics.autoFixedCount}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="自动修复成功率"
              value={data.autoFixStatistics.autoFixSuccessRate}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 自动修复进度 */}
      <Card title="自动修复进度" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>修复进度</span>
                <span>{data.autoFixStatistics.autoFixedCount} / {data.autoFixStatistics.autoFixableCount}</span>
              </div>
              <Progress
                percent={
                  data.autoFixStatistics.autoFixableCount > 0
                    ? (data.autoFixStatistics.autoFixedCount / data.autoFixStatistics.autoFixableCount) * 100
                    : 0
                }
                status="active"
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
            </div>
          </Col>
          <Col span={12}>
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>成功率</span>
                <span>{data.autoFixStatistics.autoFixSuccessRate}</span>
              </div>
              <Progress
                percent={parseFloat(data.autoFixStatistics.autoFixSuccessRate.replace('%', ''))}
                status="active"
                strokeColor={{
                  '0%': '#ff4d4f',
                  '50%': '#faad14',
                  '100%': '#52c41a',
                }}
              />
            </div>
          </Col>
        </Row>
      </Card>

      {/* 统计表格 */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card
            title="处理状态统计"
            extra={
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={onRefresh}
                loading={loading}
                size="small"
              >
                刷新
              </Button>
            }
          >
            <Table
              columns={statusColumns}
              dataSource={data.statusStatistics}
              pagination={false}
              size="small"
              rowKey="processStatus"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="异常类型统计">
            <Table
              columns={typeColumns}
              dataSource={data.typeStatistics}
              pagination={false}
              size="small"
              rowKey="anomalyType"
            />
          </Card>
        </Col>
      </Row>

      {/* 详细分析 */}
      <Card title="详细分析" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card size="small">
              <Statistic
                title="待处理异常"
                value={data.statusStatistics.find(s => s.processStatus === 'pending')?.count || 0}
                valueStyle={{ color: '#faad14' }}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                需要及时处理的异常数量
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <Statistic
                title="修复失败异常"
                value={data.statusStatistics.find(s => s.processStatus === 'auto_fix_failed')?.count || 0}
                valueStyle={{ color: '#ff4d4f' }}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                自动修复失败，需要人工处理
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <Statistic
                title="已忽略异常"
                value={data.statusStatistics.find(s => s.processStatus === 'ignored')?.count || 0}
                valueStyle={{ color: '#8c8c8c' }}
              />
              <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                已确认为正常的异常记录
              </div>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 建议和提醒 */}
      <Card title="处理建议" style={{ marginTop: 16 }}>
        <div style={{ backgroundColor: '#f6ffed', padding: 16, borderRadius: 6, border: '1px solid #b7eb8f' }}>
          <Title level={5} style={{ color: '#52c41a', marginBottom: 8 }}>
            系统建议
          </Title>
          <ul style={{ marginBottom: 0, color: '#52c41a' }}>
            {data.totalAnomalies === 0 && (
              <li>当前没有异常记录，系统运行正常</li>
            )}
            {data.statusStatistics.find(s => s.processStatus === 'pending')?.count > 0 && (
              <li>有 {data.statusStatistics.find(s => s.processStatus === 'pending')?.count} 个待处理异常，建议及时处理</li>
            )}
            {data.autoFixStatistics.autoFixableCount > data.autoFixStatistics.autoFixedCount && (
              <li>有 {data.autoFixStatistics.autoFixableCount - data.autoFixStatistics.autoFixedCount} 个可自动修复的异常，建议执行批量自动修复</li>
            )}
            {data.statusStatistics.find(s => s.processStatus === 'auto_fix_failed')?.count > 0 && (
              <li>有 {data.statusStatistics.find(s => s.processStatus === 'auto_fix_failed')?.count} 个自动修复失败的异常，需要人工处理</li>
            )}
            {parseFloat(data.autoFixStatistics.autoFixSuccessRate.replace('%', '')) < 80 && (
              <li>自动修复成功率较低（{data.autoFixStatistics.autoFixSuccessRate}），建议检查修复规则</li>
            )}
          </ul>
        </div>
      </Card>
      </div>
    </Spin>
  );
};

export default StatisticsReport;
