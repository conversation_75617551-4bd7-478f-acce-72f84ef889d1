import {
  But<PERSON>,
  Card,
  Col,
  Form,
  InputNumber,
  Row,
  Space,
  Typography,
  Tooltip,
  Divider,
  Modal,
  Select,
} from 'antd';
import {
  ToolOutlined,
  ThunderboltOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import React, { useState } from 'react';

const { Title, Text } = Typography;
const { Option } = Select;

interface FixOperationPanelProps {
  loading: boolean;
  onBatchAutoFix: (params?: any) => void;
  onRefresh: () => void;
}

/**
 * 修复操作面板组件
 */
const FixOperationPanel: React.FC<FixOperationPanelProps> = ({
  loading,
  onBatchAutoFix,
  onRefresh,
}) => {
  const [form] = Form.useForm();
  const [batchFixParams, setBatchFixParams] = useState({
    maxAttempts: 3,
    onlyAutoFixable: 'true',
  });

  // 批量自动修复
  const handleBatchAutoFix = () => {
    Modal.confirm({
      title: '确认批量自动修复',
      content: (
        <div>
          <p>此操作将批量修复所有可自动修复的异常记录。</p>
          <p><strong>最大尝试次数：</strong>{batchFixParams.maxAttempts}</p>
          <p><strong>仅修复可自动修复的：</strong>{batchFixParams.onlyAutoFixable === 'true' ? '是' : '否'}</p>
          <p style={{ color: '#ff4d4f' }}>请确认在业务低峰期执行此操作。</p>
        </div>
      ),
      icon: <ExclamationCircleOutlined />,
      okText: '确认执行',
      cancelText: '取消',
      okButtonProps: { danger: true },
      onOk: () => onBatchAutoFix(batchFixParams),
    });
  };

  // 批量修复指定记录
  const handleBatchFixSpecific = () => {
    Modal.confirm({
      title: '批量修复指定记录',
      content: '请先在异常记录列表中选择要修复的记录，然后使用此功能。',
      icon: <ExclamationCircleOutlined />,
      okText: '了解',
      cancelText: '取消',
      onOk: () => {
        // 这里可以实现选择特定记录的批量修复逻辑
        console.log('批量修复指定记录');
      },
    });
  };

  return (
    <div>
      {/* 批量修复参数配置 */}
      <Card title="批量修复参数配置" size="small" style={{ marginBottom: 16 }}>
        <Form
          form={form}
          layout="inline"
          initialValues={batchFixParams}
          onValuesChange={(_, values) => setBatchFixParams(values)}
        >
          <Form.Item
            label="最大尝试次数"
            name="maxAttempts"
            tooltip="每个异常记录的最大修复尝试次数"
          >
            <InputNumber
              min={1}
              max={10}
              style={{ width: 120 }}
            />
          </Form.Item>
          <Form.Item
            label="仅修复可自动修复的"
            name="onlyAutoFixable"
            tooltip="是否只修复标记为可自动修复的异常"
          >
            <Select style={{ width: 120 }}>
              <Option value="true">是</Option>
              <Option value="false">否</Option>
            </Select>
          </Form.Item>
        </Form>
      </Card>

      {/* 批量修复操作 */}
      <Card title="批量修复操作" size="small">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Title level={5}>
              <ThunderboltOutlined style={{ marginRight: 8, color: '#52c41a' }} />
              自动批量修复
            </Title>
            <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
              自动修复所有可自动修复的异常记录。系统会根据预设规则自动计算正确的金额并更新订单数据。
              <br />
              <strong>注意：</strong>此操作会直接修改订单数据，建议在业务低峰期执行。
            </Text>
            <Space>
              <Tooltip title="批量自动修复所有可修复的异常">
                <Button
                  type="primary"
                  icon={<ThunderboltOutlined />}
                  loading={loading}
                  onClick={handleBatchAutoFix}
                  style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                >
                  批量自动修复
                </Button>
              </Tooltip>
              <Tooltip title="刷新数据">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={onRefresh}
                  loading={loading}
                >
                  刷新数据
                </Button>
              </Tooltip>
            </Space>
          </Col>

          <Col span={24}>
            <Divider />
            <Title level={5}>
              <ToolOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              选择性批量修复
            </Title>
            <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
              批量修复指定的异常记录。您可以在异常记录列表中选择特定的记录进行批量修复。
            </Text>
            <Space>
              <Tooltip title="批量修复选中的异常记录">
                <Button
                  icon={<ToolOutlined />}
                  loading={loading}
                  onClick={handleBatchFixSpecific}
                >
                  批量修复选中记录
                </Button>
              </Tooltip>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 修复规则说明 */}
      <Card title="自动修复规则说明" size="small" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Title level={5}>修复条件</Title>
            <ul>
              <li><strong>订单状态：</strong>只能修复特定状态的订单（待付款、已付款、已确认）</li>
              <li><strong>数据完整性：</strong>订单详情数据完整，可计算出正确价格</li>
              <li><strong>业务逻辑：</strong>修复后的数据符合业务逻辑规则</li>
              <li><strong>权限检查：</strong>当前用户有修复权限</li>
            </ul>
          </Col>
          <Col span={12}>
            <Title level={5}>修复策略</Title>
            <ul>
              <li><strong>原价缺失：</strong>根据订单详情重新计算原价</li>
              <li><strong>价格不匹配：</strong>更新原价为计算值</li>
              <li><strong>实付金额错误：</strong>重新计算实付金额</li>
              <li><strong>优惠异常：</strong>调整优惠金额至合理范围</li>
            </ul>
          </Col>
        </Row>
      </Card>

      {/* 安全提示 */}
      <Card title="安全提示" size="small" style={{ marginTop: 16 }}>
        <div style={{ backgroundColor: '#fff2e8', padding: 16, borderRadius: 6 }}>
          <Title level={5} style={{ color: '#fa8c16', marginBottom: 8 }}>
            重要提醒
          </Title>
          <ul style={{ marginBottom: 0, color: '#fa8c16' }}>
            <li>批量修复操作会直接修改订单数据，请谨慎操作</li>
            <li>建议在业务低峰期执行批量修复，避免影响正常业务</li>
            <li>修复前系统会自动备份原始数据，支持回退操作</li>
            <li>所有修复操作都会记录详细日志，便于审计和追溯</li>
            <li>如发现修复错误，请及时使用回退功能恢复数据</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default FixOperationPanel;
