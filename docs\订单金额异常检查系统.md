# 订单金额异常检查系统设计文档

## 1. 系统概述

订单金额异常检查系统是为了解决系统使用过程中可能出现的订单金额不一致问题而设计的。由于人为改库、系统bug、数据迁移等原因，可能导致订单的原价、实付金额、优惠金额等字段出现不匹配的情况。

### 1.1 主要功能

- **异常检测**: 自动检测订单金额异常
- **自动修复**: 对可自动处理的异常进行修复
- **手动修复**: 提供人工干预修复功能
- **处理记录**: 完整记录所有处理过程
- **回退机制**: 支持修复操作的回退
- **统计报告**: 提供异常统计和问题清单

## 2. 异常检测规则

### 2.1 检测维度

1. **原价完整性检查**
   - 检测原价是否缺失或为0
   - 根据订单详情重新计算原价

2. **价格一致性检查**
   - 原价 = 订单详情中各服务价格之和
   - 实付金额 = 原价 - 权益卡抵扣 - 代金券抵扣

3. **优惠金额合理性检查**
   - 优惠总额不能超过订单原价
   - 权益卡和代金券抵扣金额的合理性

4. **计算逻辑检查**
   - 各字段之间的数学关系是否正确

### 2.2 异常阈值

- 默认异常阈值为1%，可配置
- 当价格差异超过阈值时标记为异常
- 根据差异程度设置严重等级（1-5级）

## 3. 异常分类

### 3.1 按类型分类

- **MISSING_ORIGINAL_PRICE**: 原价缺失
- **PRICE_MISMATCH**: 原价与计算价格不匹配
- **CALCULATION_ERROR**: 实付金额计算错误
- **DISCOUNT_ANOMALY**: 优惠金额异常
- **OTHER**: 其他异常

### 3.2 按严重程度分类

- **1级**: 轻微异常（差异<5%）
- **2级**: 一般异常（差异5-10%）
- **3级**: 中等异常（差异10-20%）
- **4级**: 严重异常（差异20-50%）
- **5级**: 极严重异常（差异≥50%）

### 3.3 按修复方式分类

- **可自动修复**: 逻辑清晰，可通过程序自动处理
- **需人工处理**: 涉及业务逻辑，需要人工判断

## 4. 修复策略

### 4.1 自动修复规则

1. **原价缺失修复**
   - 条件: 可计算出正确原价且订单状态允许修改
   - 操作: 将原价设置为计算值

2. **价格不匹配修复**
   - 条件: 计算价格合理且订单状态允许修改
   - 操作: 更新原价为计算值

3. **实付金额错误修复**
   - 条件: 订单状态允许修改
   - 操作: 重新计算实付金额

### 4.2 手动修复流程

1. 管理员查看异常详情
2. 分析异常原因
3. 确定修复方案
4. 执行修复操作
5. 记录处理过程

### 4.3 修复限制

- 只能修复特定状态的订单（待付款、已付款、已确认）
- 已完成或已评价的订单需要特殊审批
- 涉及退款的订单需要额外处理

## 5. 数据模型

### 5.1 异常记录表 (order_amount_anomaly_records)

记录发现的每个异常，包括：
- 异常基本信息（订单ID、类型、描述）
- 异常数据快照（发现时的各字段值）
- 处理状态和建议
- 处理人员和时间信息

### 5.2 修复日志表 (order_amount_fix_logs)

记录每次修复操作，包括：
- 修复前后数据对比
- 修复操作详情
- 执行结果和耗时
- 回退所需信息

## 6. 业务流程

### 6.1 异常检测流程

```
定期扫描 → 异常检测 → 创建异常记录 → 分类标记 → 生成处理建议
```

### 6.2 自动修复流程

```
获取可修复异常 → 执行修复操作 → 记录修复日志 → 更新异常状态 → 验证修复结果
```

### 6.3 手动修复流程

```
查看异常清单 → 分析异常原因 → 制定修复方案 → 执行修复操作 → 记录处理过程
```

### 6.4 回退流程

```
发现修复错误 → 查找修复日志 → 验证回退条件 → 执行回退操作 → 更新相关状态
```

## 7. 监控和报警

### 7.1 监控指标

- 异常发现数量和趋势
- 自动修复成功率
- 人工处理效率
- 回退操作频率

### 7.2 报警规则

- 异常数量突增时报警
- 自动修复失败率过高时报警
- 长期未处理的高级别异常报警

## 8. 安全和权限

### 8.1 操作权限

- 查看权限: 运营人员、管理员
- 修复权限: 高级管理员
- 回退权限: 超级管理员

### 8.2 审计要求

- 所有操作必须记录操作人
- 重要操作需要审批流程
- 定期审计修复操作的合理性

## 9. 性能考虑

### 9.1 批量处理

- 分批检测，避免一次性处理大量数据
- 异步处理，不影响正常业务
- 限制并发修复操作数量

### 9.2 数据库优化

- 为查询字段添加索引
- 定期清理历史日志数据
- 使用事务确保数据一致性

## 10. 扩展性

### 10.1 规则扩展

- 支持添加新的异常检测规则
- 支持自定义异常阈值
- 支持业务特定的修复策略

### 10.2 集成扩展

- 可与订单系统深度集成
- 支持与支付系统联动
- 可扩展到其他业务模块

## 11. 使用建议

### 11.1 日常运维

1. 每日定期执行异常检测
2. 优先处理高级别异常
3. 定期查看统计报告
4. 及时处理人工干预异常

### 11.2 应急处理

1. 发现大量异常时暂停自动修复
2. 分析异常原因，制定批量处理方案
3. 必要时回退错误的修复操作
4. 完善检测规则，避免类似问题

### 11.3 数据备份

1. 修复前自动备份原始数据
2. 定期备份异常记录和修复日志
3. 重要操作前手动创建数据快照
